#!/usr/bin/env python3
"""
Test script for the Figma Multi-Agent Design System
"""

import asyncio
import json
import sys
import os

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from figma_multi_agent_app import FigmaMultiAgentSystem, AgentType

async def test_basic_functionality():
    """Test basic functionality of the multi-agent system"""
    print("🧪 Testing Figma Multi-Agent System...")
    
    # Initialize the system
    system = FigmaMultiAgentSystem()
    await system.start_system()
    
    try:
        # Test 1: System Status
        print("\n📊 Test 1: System Status")
        status = system.get_system_status()
        print(f"✅ Agents initialized: {status['agents_count']}")
        print(f"✅ Available agents: {len(status['available_agents'])}")
        
        # Test 2: Simple Design Request
        print("\n🎨 Test 2: Simple Design Request")
        request = "Create a blue rectangle with white text saying 'Hello World'"
        result = await system.process_design_request(request)
        result_data = json.loads(result)
        print(f"✅ Request processed: {result_data['request_id']}")
        print(f"✅ Tasks completed: {result_data['tasks_completed']}")
        print(f"✅ Tasks failed: {result_data['tasks_failed']}")
        
        # Test 3: Complex Design Request
        print("\n🏗️ Test 3: Complex Design Request")
        complex_request = "Design a modern login form with email input, password field, blue submit button, and social media login options"
        result = await system.process_design_request(complex_request, ["responsive", "accessible"])
        result_data = json.loads(result)
        print(f"✅ Complex request processed: {result_data['request_id']}")
        print(f"✅ Tasks completed: {result_data['tasks_completed']}")
        
        # Test 4: Agent-specific requests
        print("\n🤖 Test 4: Agent-specific Requests")
        test_requests = [
            "Create a circular icon with a home symbol",
            "Apply a gradient background from blue to purple",
            "Add drop shadow to the main container",
            "Create a responsive grid layout with 3 columns",
            "Check accessibility compliance for color contrast"
        ]
        
        for i, req in enumerate(test_requests, 1):
            print(f"  Test 4.{i}: {req}")
            result = await system.process_design_request(req)
            result_data = json.loads(result)
            print(f"    ✅ Completed: {result_data['tasks_completed']} tasks")
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        raise
    finally:
        await system.stop_system()

def test_agent_types():
    """Test that all agent types are properly defined"""
    print("\n🔍 Testing Agent Types...")
    
    agent_types = list(AgentType)
    print(f"✅ Total agent types defined: {len(agent_types)}")
    
    expected_agents = [
        "SHAPE_CREATOR", "TEXT_HANDLER", "COLOR_STYLIST", "LAYOUT_MANAGER",
        "COMPONENT_BUILDER", "ICON_DESIGNER", "IMAGE_PROCESSOR", "ANIMATION_CREATOR",
        "GRID_DESIGNER", "TYPOGRAPHY_EXPERT", "SPACING_MANAGER", "BORDER_STYLIST",
        "SHADOW_ARTIST", "GRADIENT_CREATOR", "PATTERN_DESIGNER", "ACCESSIBILITY_CHECKER",
        "RESPONSIVE_DESIGNER", "EXPORT_SPECIALIST", "PROTOTYPE_BUILDER", "ASSET_MANAGER",
        "VERSION_CONTROLLER", "COLLABORATION_MANAGER", "PLUGIN_INTEGRATOR", "DESIGN_SYSTEM_MANAGER"
    ]
    
    for agent in expected_agents:
        if hasattr(AgentType, agent):
            print(f"  ✅ {agent}")
        else:
            print(f"  ❌ Missing: {agent}")
    
    print(f"✅ Agent types verification complete")

async def main():
    """Main test function"""
    print("🚀 Starting Figma Multi-Agent System Tests")
    print("=" * 50)
    
    # Test agent types first
    test_agent_types()
    
    # Test basic functionality
    await test_basic_functionality()
    
    print("\n" + "=" * 50)
    print("✨ All tests completed! The system is ready to use.")
    print("\nTo run the application:")
    print("  Streamlit UI: python figma_multi_agent_app.py streamlit")
    print("  FastAPI Server: python figma_multi_agent_app.py api")
    print("  Default (Streamlit): python figma_multi_agent_app.py")

if __name__ == "__main__":
    asyncio.run(main())
