#!/usr/bin/env python3
"""
Multi-Agent Figma UI Design Application
Built with OpenAI SDK Agent + Tools pattern using Gemini-1.5-flash
Features 20+ specialized agents with async processing and intelligent routing
"""

import asyncio
import json
import logging
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import traceback

# Core imports
import google.generativeai as genai
import streamlit as st
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from threading import Thread
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('figma_agents.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure Gemini API
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# Simple Agent implementation for this demo
class Agent:
    """Simple Agent implementation using Gemini"""

    def __init__(self, name: str, instructions: str, model: str = "gemini-1.5-flash", tools: List = None):
        self.name = name
        self.instructions = instructions
        self.model = model
        self.tools = tools or []
        self.client = genai.GenerativeModel(model_name=model)

    def run(self, query: str) -> str:
        """Run the agent with a query"""
        try:
            # Combine instructions with query
            full_prompt = f"{self.instructions}\n\nUser Request: {query}\n\nPlease provide a detailed response."

            # Generate response using Gemini
            response = self.client.generate_content(full_prompt)

            # If tools are available, simulate tool usage
            if self.tools and any(keyword in query.lower() for keyword in ['create', 'make', 'build', 'design']):
                # Simulate tool execution for demo purposes
                tool_result = {
                    "agent": self.name,
                    "action": "simulated_tool_execution",
                    "query": query,
                    "result": response.text if response.text else "Task completed successfully",
                    "timestamp": datetime.now().isoformat()
                }
                return json.dumps(tool_result)

            return response.text if response.text else "Task completed successfully"

        except Exception as e:
            logger.error(f"Error in agent {self.name}: {str(e)}")
            return f"Error: {str(e)}"

def function_tool(func):
    """Simple function tool decorator"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in tool {func.__name__}: {str(e)}")
            return json.dumps({"error": str(e), "function": func.__name__})

    wrapper.__name__ = func.__name__
    wrapper.__doc__ = func.__doc__
    return wrapper

class AgentType(Enum):
    """Enumeration of all specialized agent types"""
    SHAPE_CREATOR = "shape_creator"
    TEXT_HANDLER = "text_handler"
    COLOR_STYLIST = "color_stylist"
    LAYOUT_MANAGER = "layout_manager"
    COMPONENT_BUILDER = "component_builder"
    LAYER_ORGANIZER = "layer_organizer"
    EXPORT_SPECIALIST = "export_specialist"
    PROTOTYPE_BUILDER = "prototype_builder"
    ICON_DESIGNER = "icon_designer"
    IMAGE_PROCESSOR = "image_processor"
    ANIMATION_CREATOR = "animation_creator"
    GRID_DESIGNER = "grid_designer"
    TYPOGRAPHY_EXPERT = "typography_expert"
    SPACING_MANAGER = "spacing_manager"
    BORDER_STYLIST = "border_stylist"
    SHADOW_ARTIST = "shadow_artist"
    GRADIENT_CREATOR = "gradient_creator"
    PATTERN_DESIGNER = "pattern_designer"
    ACCESSIBILITY_CHECKER = "accessibility_checker"
    RESPONSIVE_DESIGNER = "responsive_designer"
    ASSET_MANAGER = "asset_manager"
    VERSION_CONTROLLER = "version_controller"
    COLLABORATION_MANAGER = "collaboration_manager"
    PLUGIN_INTEGRATOR = "plugin_integrator"
    DESIGN_SYSTEM_MANAGER = "design_system_manager"

@dataclass
class AgentTask:
    """Represents a task for an agent"""
    id: str
    agent_type: AgentType
    description: str
    parameters: Dict[str, Any]
    priority: int = 1
    status: str = "pending"
    result: Optional[Any] = None
    created_at: datetime = None
    completed_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class FigmaDesignRequest:
    """Represents a complete design request"""
    id: str
    description: str
    requirements: List[str]
    tasks: List[AgentTask]
    status: str = "processing"
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class AgentRegistry:
    """Registry for all specialized agents"""
    
    def __init__(self):
        self.agents: Dict[AgentType, Agent] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.results: Dict[str, Any] = {}
        self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize all specialized agents"""
        logger.info("Initializing specialized agents...")
        
        # Shape Creator Agent
        self.agents[AgentType.SHAPE_CREATOR] = Agent(
            name="Shape Creator",
            instructions="""You are a specialized Figma shape creation agent. You excel at creating geometric shapes, 
            custom paths, and complex vector graphics. Always provide precise coordinates and styling.""",
            model="gemini-1.5-flash",
            tools=[self._create_shape_tool()]
        )
        
        # Text Handler Agent
        self.agents[AgentType.TEXT_HANDLER] = Agent(
            name="Text Handler",
            instructions="""You are a specialized text and typography agent for Figma. You handle text creation, 
            formatting, font selection, and text styling with precision.""",
            model="gemini-1.5-flash",
            tools=[self._create_text_tool()]
        )
        
        # Color Stylist Agent
        self.agents[AgentType.COLOR_STYLIST] = Agent(
            name="Color Stylist",
            instructions="""You are a color and styling expert for Figma designs. You create color palettes, 
            apply colors, manage gradients, and ensure color accessibility.""",
            model="gemini-1.5-flash",
            tools=[self._create_color_tool()]
        )
        
        # Layout Manager Agent
        self.agents[AgentType.LAYOUT_MANAGER] = Agent(
            name="Layout Manager",
            instructions="""You are a layout and positioning specialist. You handle element positioning, 
            alignment, spacing, and overall layout structure in Figma designs.""",
            model="gemini-1.5-flash",
            tools=[self._create_layout_tool()]
        )
        
        # Component Builder Agent
        self.agents[AgentType.COMPONENT_BUILDER] = Agent(
            name="Component Builder",
            instructions="""You are a component and instance management expert. You create reusable components,
            manage variants, and handle component libraries.""",
            model="gemini-1.5-flash",
            tools=[self._create_component_tool()]
        )

        # Additional Specialized Agents
        self.agents[AgentType.ICON_DESIGNER] = Agent(
            name="Icon Designer",
            instructions="""You are an icon design specialist. You create custom icons, icon sets,
            and manage icon libraries with consistent styling.""",
            model="gemini-1.5-flash",
            tools=[self._create_icon_tool()]
        )

        self.agents[AgentType.IMAGE_PROCESSOR] = Agent(
            name="Image Processor",
            instructions="""You are an image processing expert. You handle image imports,
            cropping, filters, and image optimization for Figma designs.""",
            model="gemini-1.5-flash",
            tools=[self._create_image_tool()]
        )

        self.agents[AgentType.ANIMATION_CREATOR] = Agent(
            name="Animation Creator",
            instructions="""You are an animation and interaction specialist. You create smooth transitions,
            micro-interactions, and animated prototypes.""",
            model="gemini-1.5-flash",
            tools=[self._create_animation_tool()]
        )

        self.agents[AgentType.GRID_DESIGNER] = Agent(
            name="Grid Designer",
            instructions="""You are a grid and layout system expert. You create responsive grids,
            layout systems, and ensure proper alignment.""",
            model="gemini-1.5-flash",
            tools=[self._create_grid_tool()]
        )

        self.agents[AgentType.TYPOGRAPHY_EXPERT] = Agent(
            name="Typography Expert",
            instructions="""You are a typography specialist. You manage font hierarchies,
            text styles, and ensure typographic consistency.""",
            model="gemini-1.5-flash",
            tools=[self._create_typography_tool()]
        )

        self.agents[AgentType.SPACING_MANAGER] = Agent(
            name="Spacing Manager",
            instructions="""You are a spacing and padding expert. You ensure consistent spacing,
            manage white space, and create harmonious layouts.""",
            model="gemini-1.5-flash",
            tools=[self._create_spacing_tool()]
        )

        self.agents[AgentType.BORDER_STYLIST] = Agent(
            name="Border Stylist",
            instructions="""You are a border and stroke specialist. You create custom borders,
            manage stroke styles, and handle outline effects.""",
            model="gemini-1.5-flash",
            tools=[self._create_border_tool()]
        )

        self.agents[AgentType.SHADOW_ARTIST] = Agent(
            name="Shadow Artist",
            instructions="""You are a shadow and depth expert. You create drop shadows,
            inner shadows, and depth effects for visual hierarchy.""",
            model="gemini-1.5-flash",
            tools=[self._create_shadow_tool()]
        )

        self.agents[AgentType.GRADIENT_CREATOR] = Agent(
            name="Gradient Creator",
            instructions="""You are a gradient and color transition specialist. You create beautiful gradients,
            color transitions, and advanced fill effects.""",
            model="gemini-1.5-flash",
            tools=[self._create_gradient_tool()]
        )

        self.agents[AgentType.PATTERN_DESIGNER] = Agent(
            name="Pattern Designer",
            instructions="""You are a pattern and texture expert. You create repeating patterns,
            textures, and decorative elements.""",
            model="gemini-1.5-flash",
            tools=[self._create_pattern_tool()]
        )

        self.agents[AgentType.ACCESSIBILITY_CHECKER] = Agent(
            name="Accessibility Checker",
            instructions="""You are an accessibility expert. You ensure designs meet WCAG guidelines,
            check color contrast, and validate accessibility standards.""",
            model="gemini-1.5-flash",
            tools=[self._create_accessibility_tool()]
        )

        self.agents[AgentType.RESPONSIVE_DESIGNER] = Agent(
            name="Responsive Designer",
            instructions="""You are a responsive design specialist. You create adaptive layouts,
            manage breakpoints, and ensure cross-device compatibility.""",
            model="gemini-1.5-flash",
            tools=[self._create_responsive_tool()]
        )

        self.agents[AgentType.EXPORT_SPECIALIST] = Agent(
            name="Export Specialist",
            instructions="""You are an export and asset generation expert. You handle file exports,
            optimize assets, and manage different file formats.""",
            model="gemini-1.5-flash",
            tools=[self._create_export_tool()]
        )

        self.agents[AgentType.PROTOTYPE_BUILDER] = Agent(
            name="Prototype Builder",
            instructions="""You are a prototyping expert. You create interactive prototypes,
            define user flows, and build clickable mockups.""",
            model="gemini-1.5-flash",
            tools=[self._create_prototype_tool()]
        )

        self.agents[AgentType.ASSET_MANAGER] = Agent(
            name="Asset Manager",
            instructions="""You are an asset management specialist. You organize design assets,
            manage libraries, and maintain design systems.""",
            model="gemini-1.5-flash",
            tools=[self._create_asset_tool()]
        )

        self.agents[AgentType.VERSION_CONTROLLER] = Agent(
            name="Version Controller",
            instructions="""You are a version control expert. You manage design versions,
            track changes, and handle design history.""",
            model="gemini-1.5-flash",
            tools=[self._create_version_tool()]
        )

        self.agents[AgentType.COLLABORATION_MANAGER] = Agent(
            name="Collaboration Manager",
            instructions="""You are a collaboration specialist. You manage team workflows,
            handle comments, and facilitate design reviews.""",
            model="gemini-1.5-flash",
            tools=[self._create_collaboration_tool()]
        )

        self.agents[AgentType.PLUGIN_INTEGRATOR] = Agent(
            name="Plugin Integrator",
            instructions="""You are a plugin integration expert. You manage Figma plugins,
            automate workflows, and extend Figma functionality.""",
            model="gemini-1.5-flash",
            tools=[self._create_plugin_tool()]
        )

        self.agents[AgentType.DESIGN_SYSTEM_MANAGER] = Agent(
            name="Design System Manager",
            instructions="""You are a design system expert. You create and maintain design systems,
            manage tokens, and ensure consistency across projects.""",
            model="gemini-1.5-flash",
            tools=[self._create_design_system_tool()]
        )

        logger.info(f"Initialized {len(self.agents)} specialized agents")
    
    def _create_shape_tool(self):
        """Create shape creation tool"""
        @function_tool
        def create_shape(shape_type: str, width: int, height: int, x: int, y: int, color: str = "#000000") -> str:
            """Create a geometric shape in Figma.
            
            :param shape_type: Type of shape (rectangle, circle, polygon, star, etc.)
            :param width: Width of the shape in pixels
            :param height: Height of the shape in pixels
            :param x: X coordinate position
            :param y: Y coordinate position
            :param color: Fill color in hex format
            """
            result = {
                "action": "create_shape",
                "shape_type": shape_type,
                "properties": {
                    "width": width,
                    "height": height,
                    "x": x,
                    "y": y,
                    "fill": color
                },
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Shape created: {shape_type} at ({x}, {y}) with size {width}x{height}")
            return json.dumps(result)
        
        return create_shape
    
    def _create_text_tool(self):
        """Create text handling tool"""
        @function_tool
        def create_text(content: str, x: int, y: int, font_family: str = "Inter", font_size: int = 16, color: str = "#000000") -> str:
            """Create and style text in Figma.
            
            :param content: The text content to create
            :param x: X coordinate position
            :param y: Y coordinate position
            :param font_family: Font family name
            :param font_size: Font size in pixels
            :param color: Text color in hex format
            """
            result = {
                "action": "create_text",
                "content": content,
                "properties": {
                    "x": x,
                    "y": y,
                    "font_family": font_family,
                    "font_size": font_size,
                    "color": color
                },
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Text created: '{content}' at ({x}, {y})")
            return json.dumps(result)
        
        return create_text
    
    def _create_color_tool(self):
        """Create color styling tool"""
        @function_tool
        def apply_color_palette(element_id: str, primary_color: str, secondary_color: str, accent_color: str) -> str:
            """Apply a color palette to design elements.
            
            :param element_id: ID of the element to style
            :param primary_color: Primary color in hex format
            :param secondary_color: Secondary color in hex format
            :param accent_color: Accent color in hex format
            """
            result = {
                "action": "apply_color_palette",
                "element_id": element_id,
                "palette": {
                    "primary": primary_color,
                    "secondary": secondary_color,
                    "accent": accent_color
                },
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Color palette applied to element {element_id}")
            return json.dumps(result)
        
        return apply_color_palette
    
    def _create_layout_tool(self):
        """Create layout management tool"""
        @function_tool
        def arrange_elements(elements: List[str], layout_type: str, spacing: int = 10) -> str:
            """Arrange multiple elements in a specific layout.
            
            :param elements: List of element IDs to arrange
            :param layout_type: Type of layout (horizontal, vertical, grid, auto)
            :param spacing: Spacing between elements in pixels
            """
            result = {
                "action": "arrange_elements",
                "elements": elements,
                "layout_type": layout_type,
                "spacing": spacing,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Arranged {len(elements)} elements in {layout_type} layout")
            return json.dumps(result)
        
        return arrange_elements
    
    def _create_component_tool(self):
        """Create component management tool"""
        @function_tool
        def create_component(name: str, elements: List[str], variants: List[str] = None) -> str:
            """Create a reusable component from elements.
            
            :param name: Name of the component
            :param elements: List of element IDs to include in component
            :param variants: List of variant names for the component
            """
            result = {
                "action": "create_component",
                "name": name,
                "elements": elements,
                "variants": variants or [],
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Component '{name}' created with {len(elements)} elements")
            return json.dumps(result)
        
        return create_component

    def _create_icon_tool(self):
        """Create icon design tool"""
        @function_tool
        def create_icon(icon_type: str, size: int, style: str = "outline", color: str = "#000000") -> str:
            """Create a custom icon.

            :param icon_type: Type of icon (arrow, home, user, settings, etc.)
            :param size: Icon size in pixels
            :param style: Icon style (outline, filled, duotone)
            :param color: Icon color in hex format
            """
            result = {
                "action": "create_icon",
                "icon_type": icon_type,
                "size": size,
                "style": style,
                "color": color,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Icon created: {icon_type} ({style}) - {size}px")
            return json.dumps(result)
        return create_icon

    def _create_image_tool(self):
        """Create image processing tool"""
        @function_tool
        def process_image(image_path: str, operation: str, parameters: dict = None) -> str:
            """Process an image with various operations.

            :param image_path: Path to the image file
            :param operation: Operation type (crop, resize, filter, optimize)
            :param parameters: Operation-specific parameters
            """
            result = {
                "action": "process_image",
                "image_path": image_path,
                "operation": operation,
                "parameters": parameters or {},
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Image processed: {operation} on {image_path}")
            return json.dumps(result)
        return process_image

    def _create_animation_tool(self):
        """Create animation tool"""
        @function_tool
        def create_animation(element_id: str, animation_type: str, duration: float, easing: str = "ease-in-out") -> str:
            """Create an animation for an element.

            :param element_id: ID of the element to animate
            :param animation_type: Type of animation (fade, slide, scale, rotate)
            :param duration: Animation duration in seconds
            :param easing: Easing function for the animation
            """
            result = {
                "action": "create_animation",
                "element_id": element_id,
                "animation_type": animation_type,
                "duration": duration,
                "easing": easing,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Animation created: {animation_type} for {element_id}")
            return json.dumps(result)
        return create_animation

    def _create_grid_tool(self):
        """Create grid design tool"""
        @function_tool
        def create_grid(columns: int, rows: int, gap: int, container_width: int, container_height: int) -> str:
            """Create a responsive grid layout.

            :param columns: Number of columns
            :param rows: Number of rows
            :param gap: Gap between grid items in pixels
            :param container_width: Container width in pixels
            :param container_height: Container height in pixels
            """
            result = {
                "action": "create_grid",
                "columns": columns,
                "rows": rows,
                "gap": gap,
                "container_width": container_width,
                "container_height": container_height,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Grid created: {columns}x{rows} with {gap}px gap")
            return json.dumps(result)
        return create_grid

    def _create_typography_tool(self):
        """Create typography tool"""
        @function_tool
        def create_text_style(name: str, font_family: str, font_size: int, font_weight: str, line_height: float, letter_spacing: float = 0) -> str:
            """Create a text style definition.

            :param name: Name of the text style
            :param font_family: Font family name
            :param font_size: Font size in pixels
            :param font_weight: Font weight (normal, bold, 100-900)
            :param line_height: Line height multiplier
            :param letter_spacing: Letter spacing in pixels
            """
            result = {
                "action": "create_text_style",
                "name": name,
                "font_family": font_family,
                "font_size": font_size,
                "font_weight": font_weight,
                "line_height": line_height,
                "letter_spacing": letter_spacing,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Text style created: {name}")
            return json.dumps(result)
        return create_text_style

    def _create_spacing_tool(self):
        """Create spacing management tool"""
        @function_tool
        def apply_spacing(element_ids: List[str], spacing_type: str, value: int) -> str:
            """Apply consistent spacing to elements.

            :param element_ids: List of element IDs to apply spacing to
            :param spacing_type: Type of spacing (margin, padding, gap)
            :param value: Spacing value in pixels
            """
            result = {
                "action": "apply_spacing",
                "element_ids": element_ids,
                "spacing_type": spacing_type,
                "value": value,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Spacing applied: {spacing_type} {value}px to {len(element_ids)} elements")
            return json.dumps(result)
        return apply_spacing

    def _create_border_tool(self):
        """Create border styling tool"""
        @function_tool
        def apply_border(element_id: str, width: int, style: str, color: str, radius: int = 0) -> str:
            """Apply border styling to an element.

            :param element_id: ID of the element to style
            :param width: Border width in pixels
            :param style: Border style (solid, dashed, dotted)
            :param color: Border color in hex format
            :param radius: Border radius in pixels
            """
            result = {
                "action": "apply_border",
                "element_id": element_id,
                "width": width,
                "style": style,
                "color": color,
                "radius": radius,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Border applied to {element_id}: {width}px {style} {color}")
            return json.dumps(result)
        return apply_border

    def _create_shadow_tool(self):
        """Create shadow effects tool"""
        @function_tool
        def apply_shadow(element_id: str, shadow_type: str, x_offset: int, y_offset: int, blur: int, color: str, opacity: float = 0.3) -> str:
            """Apply shadow effects to an element.

            :param element_id: ID of the element to apply shadow to
            :param shadow_type: Type of shadow (drop, inner, glow)
            :param x_offset: Horizontal offset in pixels
            :param y_offset: Vertical offset in pixels
            :param blur: Blur radius in pixels
            :param color: Shadow color in hex format
            :param opacity: Shadow opacity (0.0 to 1.0)
            """
            result = {
                "action": "apply_shadow",
                "element_id": element_id,
                "shadow_type": shadow_type,
                "x_offset": x_offset,
                "y_offset": y_offset,
                "blur": blur,
                "color": color,
                "opacity": opacity,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Shadow applied to {element_id}: {shadow_type}")
            return json.dumps(result)
        return apply_shadow

    def _create_gradient_tool(self):
        """Create gradient effects tool"""
        @function_tool
        def create_gradient(element_id: str, gradient_type: str, colors: List[str], direction: str = "to right") -> str:
            """Create gradient effects for an element.

            :param element_id: ID of the element to apply gradient to
            :param gradient_type: Type of gradient (linear, radial, conic)
            :param colors: List of colors in hex format
            :param direction: Gradient direction (to right, to bottom, etc.)
            """
            result = {
                "action": "create_gradient",
                "element_id": element_id,
                "gradient_type": gradient_type,
                "colors": colors,
                "direction": direction,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Gradient created for {element_id}: {gradient_type} with {len(colors)} colors")
            return json.dumps(result)
        return create_gradient

    def _create_pattern_tool(self):
        """Create pattern design tool"""
        @function_tool
        def create_pattern(pattern_type: str, size: int, colors: List[str], repeat: str = "repeat") -> str:
            """Create repeating patterns.

            :param pattern_type: Type of pattern (dots, stripes, checkerboard, geometric)
            :param size: Pattern size in pixels
            :param colors: List of colors for the pattern
            :param repeat: Repeat behavior (repeat, no-repeat, repeat-x, repeat-y)
            """
            result = {
                "action": "create_pattern",
                "pattern_type": pattern_type,
                "size": size,
                "colors": colors,
                "repeat": repeat,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Pattern created: {pattern_type} {size}px")
            return json.dumps(result)
        return create_pattern

    def _create_accessibility_tool(self):
        """Create accessibility checking tool"""
        @function_tool
        def check_accessibility(element_id: str, check_type: str) -> str:
            """Check accessibility compliance for design elements.

            :param element_id: ID of the element to check
            :param check_type: Type of accessibility check (contrast, focus, alt-text, keyboard)
            """
            result = {
                "action": "check_accessibility",
                "element_id": element_id,
                "check_type": check_type,
                "status": "compliant",  # Simulated result
                "recommendations": ["Ensure sufficient color contrast", "Add focus indicators"],
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Accessibility check performed: {check_type} for {element_id}")
            return json.dumps(result)
        return check_accessibility

    def _create_responsive_tool(self):
        """Create responsive design tool"""
        @function_tool
        def create_responsive_layout(breakpoints: List[int], layout_rules: dict) -> str:
            """Create responsive layout rules.

            :param breakpoints: List of breakpoint widths in pixels
            :param layout_rules: Dictionary of layout rules for each breakpoint
            """
            result = {
                "action": "create_responsive_layout",
                "breakpoints": breakpoints,
                "layout_rules": layout_rules,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Responsive layout created with {len(breakpoints)} breakpoints")
            return json.dumps(result)
        return create_responsive_layout

    def _create_export_tool(self):
        """Create export tool"""
        @function_tool
        def export_assets(element_ids: List[str], format: str, scale: float = 1.0, quality: int = 100) -> str:
            """Export design assets in various formats.

            :param element_ids: List of element IDs to export
            :param format: Export format (png, jpg, svg, pdf)
            :param scale: Export scale multiplier
            :param quality: Export quality (1-100)
            """
            result = {
                "action": "export_assets",
                "element_ids": element_ids,
                "format": format,
                "scale": scale,
                "quality": quality,
                "exported_files": [f"asset_{i}.{format}" for i in range(len(element_ids))],
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Exported {len(element_ids)} assets as {format}")
            return json.dumps(result)
        return export_assets

    def _create_prototype_tool(self):
        """Create prototyping tool"""
        @function_tool
        def create_prototype_link(from_element: str, to_element: str, trigger: str, transition: str) -> str:
            """Create prototype interactions between elements.

            :param from_element: Source element ID
            :param to_element: Target element ID
            :param trigger: Interaction trigger (click, hover, drag)
            :param transition: Transition type (instant, dissolve, slide)
            """
            result = {
                "action": "create_prototype_link",
                "from_element": from_element,
                "to_element": to_element,
                "trigger": trigger,
                "transition": transition,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Prototype link created: {from_element} -> {to_element}")
            return json.dumps(result)
        return create_prototype_link

    def _create_asset_tool(self):
        """Create asset management tool"""
        @function_tool
        def organize_assets(asset_ids: List[str], category: str, tags: List[str]) -> str:
            """Organize and categorize design assets.

            :param asset_ids: List of asset IDs to organize
            :param category: Asset category (icons, images, components, colors)
            :param tags: List of tags for the assets
            """
            result = {
                "action": "organize_assets",
                "asset_ids": asset_ids,
                "category": category,
                "tags": tags,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Organized {len(asset_ids)} assets in category: {category}")
            return json.dumps(result)
        return organize_assets

    def _create_version_tool(self):
        """Create version control tool"""
        @function_tool
        def create_version(project_id: str, version_name: str, description: str) -> str:
            """Create a new version of the design.

            :param project_id: ID of the project
            :param version_name: Name of the version
            :param description: Description of changes
            """
            result = {
                "action": "create_version",
                "project_id": project_id,
                "version_name": version_name,
                "description": description,
                "version_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Version created: {version_name} for project {project_id}")
            return json.dumps(result)
        return create_version

    def _create_collaboration_tool(self):
        """Create collaboration tool"""
        @function_tool
        def add_comment(element_id: str, comment: str, author: str) -> str:
            """Add a comment to a design element.

            :param element_id: ID of the element to comment on
            :param comment: Comment text
            :param author: Author of the comment
            """
            result = {
                "action": "add_comment",
                "element_id": element_id,
                "comment": comment,
                "author": author,
                "comment_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Comment added to {element_id} by {author}")
            return json.dumps(result)
        return add_comment

    def _create_plugin_tool(self):
        """Create plugin integration tool"""
        @function_tool
        def run_plugin(plugin_name: str, parameters: dict) -> str:
            """Run a Figma plugin with specified parameters.

            :param plugin_name: Name of the plugin to run
            :param parameters: Plugin-specific parameters
            """
            result = {
                "action": "run_plugin",
                "plugin_name": plugin_name,
                "parameters": parameters,
                "status": "completed",
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Plugin executed: {plugin_name}")
            return json.dumps(result)
        return run_plugin

    def _create_design_system_tool(self):
        """Create design system management tool"""
        @function_tool
        def create_design_token(token_name: str, token_type: str, value: str, description: str) -> str:
            """Create a design system token.

            :param token_name: Name of the design token
            :param token_type: Type of token (color, spacing, typography, shadow)
            :param value: Token value
            :param description: Description of the token
            """
            result = {
                "action": "create_design_token",
                "token_name": token_name,
                "token_type": token_type,
                "value": value,
                "description": description,
                "token_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Design token created: {token_name} ({token_type})")
            return json.dumps(result)
        return create_design_token
    
    async def execute_task(self, task: AgentTask) -> Any:
        """Execute a task using the appropriate agent"""
        try:
            agent = self.agents.get(task.agent_type)
            if not agent:
                raise ValueError(f"No agent found for type: {task.agent_type}")
            
            logger.info(f"Executing task {task.id} with agent {task.agent_type.value}")
            
            # Execute the agent with the task description
            result = agent.run(task.description)
            
            task.status = "completed"
            task.result = result
            task.completed_at = datetime.now()
            
            self.results[task.id] = result
            logger.info(f"Task {task.id} completed successfully")
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing task {task.id}: {str(e)}")
            task.status = "failed"
            task.result = f"Error: {str(e)}"
            raise

class IntelligentRouter:
    """Intelligent router that analyzes queries and delegates to appropriate agents"""

    def __init__(self, agent_registry: AgentRegistry):
        self.agent_registry = agent_registry
        self.routing_agent = Agent(
            name="Router Agent",
            instructions="""You are an intelligent router for Figma design tasks. Analyze user requests and
            determine which specialized agents should handle different parts of the task. Break down complex
            requests into specific agent tasks.""",
            model="gemini-1.5-flash",
            tools=[self._create_routing_tool()]
        )

    def _create_routing_tool(self):
        """Create routing analysis tool"""
        @function_tool
        def analyze_design_request(request: str) -> str:
            """Analyze a design request and determine required agents and tasks.

            :param request: The user's design request description
            """
            # Simple keyword-based routing logic (can be enhanced with ML)
            agent_mapping = {
                "shape": AgentType.SHAPE_CREATOR,
                "rectangle": AgentType.SHAPE_CREATOR,
                "circle": AgentType.SHAPE_CREATOR,
                "text": AgentType.TEXT_HANDLER,
                "font": AgentType.TEXT_HANDLER,
                "typography": AgentType.TYPOGRAPHY_EXPERT,
                "color": AgentType.COLOR_STYLIST,
                "palette": AgentType.COLOR_STYLIST,
                "layout": AgentType.LAYOUT_MANAGER,
                "arrange": AgentType.LAYOUT_MANAGER,
                "component": AgentType.COMPONENT_BUILDER,
                "button": AgentType.COMPONENT_BUILDER,
                "export": AgentType.EXPORT_SPECIALIST,
                "prototype": AgentType.PROTOTYPE_BUILDER,
                "icon": AgentType.ICON_DESIGNER,
                "image": AgentType.IMAGE_PROCESSOR,
                "animation": AgentType.ANIMATION_CREATOR,
                "grid": AgentType.GRID_DESIGNER,
                "spacing": AgentType.SPACING_MANAGER,
                "border": AgentType.BORDER_STYLIST,
                "shadow": AgentType.SHADOW_ARTIST,
                "gradient": AgentType.GRADIENT_CREATOR,
                "accessibility": AgentType.ACCESSIBILITY_CHECKER,
                "responsive": AgentType.RESPONSIVE_DESIGNER
            }

            request_lower = request.lower()
            required_agents = []

            for keyword, agent_type in agent_mapping.items():
                if keyword in request_lower:
                    required_agents.append(agent_type.value)

            # Default to shape creator if no specific agents identified
            if not required_agents:
                required_agents.append(AgentType.SHAPE_CREATOR.value)

            result = {
                "required_agents": required_agents,
                "analysis": f"Identified {len(required_agents)} relevant agents for the request",
                "request": request
            }

            return json.dumps(result)

        return analyze_design_request

    async def route_request(self, request: str) -> List[AgentTask]:
        """Route a design request to appropriate agents"""
        try:
            # Analyze the request
            analysis_result = self.routing_agent.run(f"Analyze this design request: {request}")

            # Parse the analysis (in a real implementation, this would be more sophisticated)
            tasks = []
            task_id = str(uuid.uuid4())

            # Create tasks based on request analysis
            # For demo purposes, create a few sample tasks
            if "shape" in request.lower() or "rectangle" in request.lower():
                tasks.append(AgentTask(
                    id=f"{task_id}_shape",
                    agent_type=AgentType.SHAPE_CREATOR,
                    description=f"Create shapes based on: {request}",
                    parameters={"request": request}
                ))

            if "text" in request.lower():
                tasks.append(AgentTask(
                    id=f"{task_id}_text",
                    agent_type=AgentType.TEXT_HANDLER,
                    description=f"Handle text elements for: {request}",
                    parameters={"request": request}
                ))

            if "color" in request.lower():
                tasks.append(AgentTask(
                    id=f"{task_id}_color",
                    agent_type=AgentType.COLOR_STYLIST,
                    description=f"Apply colors and styling for: {request}",
                    parameters={"request": request}
                ))

            # If no specific tasks identified, create a general design task
            if not tasks:
                tasks.append(AgentTask(
                    id=f"{task_id}_general",
                    agent_type=AgentType.SHAPE_CREATOR,
                    description=f"Create design elements for: {request}",
                    parameters={"request": request}
                ))

            logger.info(f"Routed request to {len(tasks)} agents")
            return tasks

        except Exception as e:
            logger.error(f"Error routing request: {str(e)}")
            raise

class FigmaMultiAgentSystem:
    """Main system orchestrating all agents and operations"""

    def __init__(self):
        self.agent_registry = AgentRegistry()
        self.router = IntelligentRouter(self.agent_registry)
        self.active_requests: Dict[str, FigmaDesignRequest] = {}
        self.task_executor = None

    async def start_system(self):
        """Start the multi-agent system"""
        logger.info("Starting Figma Multi-Agent System...")
        self.task_executor = asyncio.create_task(self._task_processor())
        logger.info("System started successfully")

    async def stop_system(self):
        """Stop the multi-agent system"""
        if self.task_executor:
            self.task_executor.cancel()
        logger.info("System stopped")

    async def _task_processor(self):
        """Background task processor"""
        while True:
            try:
                # Process tasks from queue
                if not self.agent_registry.task_queue.empty():
                    task = await self.agent_registry.task_queue.get()
                    await self.agent_registry.execute_task(task)

                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in task processor: {str(e)}")

    async def process_design_request(self, description: str, requirements: List[str] = None) -> str:
        """Process a complete design request"""
        try:
            request_id = str(uuid.uuid4())

            # Route the request to appropriate agents
            tasks = await self.router.route_request(description)

            # Create design request
            design_request = FigmaDesignRequest(
                id=request_id,
                description=description,
                requirements=requirements or [],
                tasks=tasks
            )

            self.active_requests[request_id] = design_request

            # Execute tasks concurrently
            task_results = await asyncio.gather(
                *[self.agent_registry.execute_task(task) for task in tasks],
                return_exceptions=True
            )

            # Update request status
            design_request.status = "completed"

            # Compile results
            results = {
                "request_id": request_id,
                "description": description,
                "tasks_completed": len([r for r in task_results if not isinstance(r, Exception)]),
                "tasks_failed": len([r for r in task_results if isinstance(r, Exception)]),
                "results": [str(r) for r in task_results if not isinstance(r, Exception)],
                "errors": [str(r) for r in task_results if isinstance(r, Exception)]
            }

            logger.info(f"Design request {request_id} completed")
            return json.dumps(results, indent=2)

        except Exception as e:
            logger.error(f"Error processing design request: {str(e)}")
            raise

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "agents_count": len(self.agent_registry.agents),
            "active_requests": len(self.active_requests),
            "queue_size": self.agent_registry.task_queue.qsize(),
            "available_agents": [agent_type.value for agent_type in self.agent_registry.agents.keys()],
            "timestamp": datetime.now().isoformat()
        }

# Initialize the global system
figma_system = FigmaMultiAgentSystem()

# FastAPI REST API
app = FastAPI(
    title="Figma Multi-Agent Design System",
    description="REST API for the Figma Multi-Agent Design System",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    await figma_system.start_system()

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    await figma_system.stop_system()

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Figma Multi-Agent Design System API", "status": "running"}

@app.get("/status")
async def get_status():
    """Get system status"""
    return figma_system.get_system_status()

@app.post("/design")
async def create_design(request: dict):
    """Create a new design using the multi-agent system"""
    try:
        description = request.get("description", "")
        requirements = request.get("requirements", [])

        if not description:
            raise HTTPException(status_code=400, detail="Description is required")

        result = await figma_system.process_design_request(description, requirements)
        return {"success": True, "result": json.loads(result)}

    except Exception as e:
        logger.error(f"API error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agents")
async def list_agents():
    """List all available agents"""
    return {
        "agents": [
            {
                "type": agent_type.value,
                "name": agent_type.value.replace("_", " ").title(),
                "description": f"Specialized agent for {agent_type.value.replace('_', ' ')}"
            }
            for agent_type in AgentType
        ]
    }

def run_api_server():
    """Run the FastAPI server"""
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

def create_streamlit_interface():
    """Create the Streamlit user interface"""
    st.set_page_config(
        page_title="Figma Multi-Agent Design System",
        page_icon="🎨",
        layout="wide"
    )

    st.title("🎨 Figma Multi-Agent Design System")
    st.markdown("### Powered by 25+ Specialized AI Agents")

    # Sidebar with system information
    with st.sidebar:
        st.header("System Status")

        if st.button("Refresh Status"):
            status = figma_system.get_system_status()
            st.json(status)

        st.header("Available Agents")
        agent_types = [agent_type.value.replace("_", " ").title() for agent_type in AgentType]
        st.write(f"**{len(agent_types)} Specialized Agents:**")
        for agent in agent_types[:10]:  # Show first 10
            st.write(f"• {agent}")
        if len(agent_types) > 10:
            st.write(f"• ... and {len(agent_types) - 10} more")

    # Main interface
    st.header("Create Your Design")

    # Design request input
    design_description = st.text_area(
        "Describe your design request:",
        placeholder="e.g., Create a modern login form with blue color scheme, rounded buttons, and clean typography",
        height=100
    )

    # Requirements input
    requirements = st.text_input(
        "Additional requirements (comma-separated):",
        placeholder="responsive, accessible, modern, minimalist"
    )

    # Process button
    if st.button("🚀 Generate Design", type="primary"):
        if design_description:
            with st.spinner("Processing your design request..."):
                try:
                    # Initialize system if not already done
                    if not hasattr(st.session_state, 'system_initialized'):
                        asyncio.run(figma_system.start_system())
                        st.session_state.system_initialized = True

                    # Process the request
                    requirements_list = [req.strip() for req in requirements.split(",") if req.strip()] if requirements else []
                    result = asyncio.run(figma_system.process_design_request(design_description, requirements_list))

                    # Display results
                    st.success("Design request processed successfully!")

                    # Parse and display results
                    result_data = json.loads(result)

                    col1, col2 = st.columns(2)

                    with col1:
                        st.metric("Tasks Completed", result_data.get("tasks_completed", 0))
                        st.metric("Tasks Failed", result_data.get("tasks_failed", 0))

                    with col2:
                        st.metric("Request ID", result_data.get("request_id", "N/A"))

                    # Show detailed results
                    if result_data.get("results"):
                        st.subheader("Agent Results")
                        for i, agent_result in enumerate(result_data["results"]):
                            with st.expander(f"Agent Result {i+1}"):
                                try:
                                    parsed_result = json.loads(agent_result)
                                    st.json(parsed_result)
                                except:
                                    st.text(agent_result)

                    # Show errors if any
                    if result_data.get("errors"):
                        st.subheader("Errors")
                        for error in result_data["errors"]:
                            st.error(error)

                except Exception as e:
                    st.error(f"Error processing request: {str(e)}")
                    logger.error(f"Streamlit error: {str(e)}")
        else:
            st.warning("Please enter a design description")

    # Example requests
    st.header("Example Requests")
    examples = [
        "Create a modern dashboard with charts and data visualization",
        "Design a mobile app login screen with social media buttons",
        "Build a landing page hero section with call-to-action button",
        "Create a card component with image, title, and description",
        "Design a navigation menu with dropdown functionality"
    ]

    for example in examples:
        if st.button(f"Try: {example}", key=f"example_{hash(example)}"):
            st.session_state.example_request = example
            st.rerun()

    # Auto-fill example if selected
    if hasattr(st.session_state, 'example_request'):
        st.text_area(
            "Selected example:",
            value=st.session_state.example_request,
            disabled=True,
            key="example_display"
        )

def main():
    """Main application entry point"""
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "api":
            # Run API server
            print("Starting FastAPI server...")
            run_api_server()
        elif sys.argv[1] == "streamlit":
            # Run Streamlit interface
            print("Starting Streamlit interface...")
            create_streamlit_interface()
        else:
            print("Usage: python figma_multi_agent_app.py [api|streamlit]")
    else:
        # Default: run Streamlit interface
        print("Starting Streamlit interface (default)...")
        create_streamlit_interface()

if __name__ == "__main__":
    main()
