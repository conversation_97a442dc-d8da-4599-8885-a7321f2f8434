#!/usr/bin/env python3
"""
Multi-Agent Figma UI Design Application
Built with OpenAI SDK Agent + Tools pattern using Gemini-1.5-flash
Features 25 specialized agents with async processing and intelligent routing

Refactored for better code organization:
- agents.py: Contains all agent definitions and logic
- tools.py: Contains all tool implementations
- figma_multi_agent_app.py: Main orchestration and interfaces
"""

import asyncio
import json
import logging
import os
import uuid
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import traceback

# Core imports
import google.generativeai as genai
import streamlit as st
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from threading import Thread
import time

# Import our modular components
from agents import Agent, AgentType, AgentTask, AGENT_SPECIFICATIONS
from tools import FigmaTools, get_tools_for_agent, function_tool, set_mcp_system

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('figma_agents.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure Gemini API
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# Wrapper to handle async functions in sync context
def async_to_sync(func):
    """Convert async function to sync for Streamlit compatibility"""
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(func(*args, **kwargs))
    wrapper.__doc__ = func.__doc__
    return wrapper

# MCP Configuration
MCP_SERVER_COMMAND = "bun"
MCP_SERVER_PATH = r"C:\Users\<USER>\Desktop\claudetalktofigma\claude-talk-to-figma-mcp\src\talk_to_figma_mcp\server.ts"

class MCPClient:
    """
    Model Context Protocol (MCP) Client for communicating with external MCP servers
    Implements stdio-based communication with the Figma MCP server
    """

    def __init__(self, command: str, server_path: str):
        self.command = command
        self.server_path = server_path
        self.process = None
        self.available_tools = []
        self.connected = False

    async def connect(self) -> bool:
        """Connect to the MCP server"""
        try:
            logger.info(f"Connecting to MCP server: {self.command} {self.server_path}")

            # Start the MCP server process
            self.process = subprocess.Popen(
                [self.command, self.server_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )

            # Initialize MCP protocol
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "figma-multi-agent-system",
                        "version": "1.0.0"
                    }
                }
            }

            # Send initialization request
            self.process.stdin.write(json.dumps(init_request) + "\n")
            self.process.stdin.flush()

            # Read initialization response
            response_line = self.process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                if "result" in response:
                    logger.info("MCP server initialized successfully")
                    self.connected = True

                    # Get available tools
                    await self._fetch_tools()
                    return True

        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            if self.process:
                self.process.terminate()
                self.process = None

        return False

    async def _fetch_tools(self):
        """Fetch available tools from the MCP server"""
        try:
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }

            self.process.stdin.write(json.dumps(tools_request) + "\n")
            self.process.stdin.flush()

            response_line = self.process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                if "result" in response and "tools" in response["result"]:
                    self.available_tools = response["result"]["tools"]
                    logger.info(f"Fetched {len(self.available_tools)} MCP tools")

        except Exception as e:
            logger.error(f"Failed to fetch MCP tools: {e}")

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server"""
        if not self.connected or not self.process:
            raise Exception("MCP server not connected")

        try:
            tool_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            self.process.stdin.write(json.dumps(tool_request) + "\n")
            self.process.stdin.flush()

            response_line = self.process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                if "result" in response:
                    return response["result"]
                elif "error" in response:
                    raise Exception(f"MCP tool error: {response['error']}")

        except Exception as e:
            logger.error(f"Failed to call MCP tool {tool_name}: {e}")
            raise

        return {}

    def disconnect(self):
        """Disconnect from the MCP server"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
            finally:
                self.process = None
                self.connected = False
                logger.info("Disconnected from MCP server")

@dataclass
class FigmaDesignRequest:
    """Represents a complete design request"""
    id: str
    description: str
    requirements: List[str]
    tasks: List[AgentTask]
    status: str = "processing"
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class AgentRegistry:
    """Registry for all specialized agents using modular architecture"""

    def __init__(self):
        self.agents: Dict[AgentType, Agent] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.results: Dict[str, Any] = {}
        self._initialize_agents()

    def _initialize_agents(self):
        """Initialize all specialized agents using the modular system"""
        logger.info("Initializing specialized agents...")

        # Initialize all 25 agents using the specifications from agents.py
        for agent_type, spec in AGENT_SPECIFICATIONS.items():
            # Get appropriate tools for this agent type
            tools = get_tools_for_agent(agent_type.value)

            # Create agent using the specification
            self.agents[agent_type] = Agent(
                name=spec["name"],
                instructions=spec["instructions"],
                model="gemini-1.5-flash",
                tools=tools
            )

        logger.info(f"Initialized {len(self.agents)} specialized agents")

    async def get_agent(self, agent_type: AgentType) -> Agent:
        """Get an agent by type"""
        return self.agents.get(agent_type)

    async def execute_task(self, task: AgentTask) -> str:
        """Execute a task using the appropriate agent"""
        agent = await self.get_agent(task.agent_type)
        if not agent:
            raise ValueError(f"No agent found for type: {task.agent_type}")

        try:
            task.status = "running"
            result = agent.run(task.description)
            task.status = "completed"
            task.result = result
            task.completed_at = datetime.now()
            return result
        except Exception as e:
            logger.error(f"Error executing task {task.id}: {str(e)}")
            task.status = "failed"
            task.result = f"Error: {str(e)}"
            raise


class IntelligentRouter:
    """Intelligent router that analyzes queries and delegates to appropriate agents"""

    def __init__(self, agent_registry: AgentRegistry):
        self.agent_registry = agent_registry
        self.routing_agent = Agent(
            name="Router Agent",
            instructions="""You are an intelligent router for Figma design tasks. Analyze user requests and
            determine which specialized agents should handle different parts of the task. Break down complex
            requests into specific agent tasks.""",
            model="gemini-1.5-flash",
            tools=[self._create_routing_tool()]
        )

    def _create_routing_tool(self):
        """Create routing analysis tool"""
        @function_tool
        def analyze_design_request(request: str) -> str:
            """Analyze a design request and determine required agents and tasks.

            :param request: The user's design request description
            """
            # Simple keyword-based routing logic (can be enhanced with ML)
            agent_mapping = {
                "shape": AgentType.SHAPE_CREATOR,
                "rectangle": AgentType.SHAPE_CREATOR,
                "circle": AgentType.SHAPE_CREATOR,
                "text": AgentType.TEXT_HANDLER,
                "font": AgentType.TEXT_HANDLER,
                "typography": AgentType.TYPOGRAPHY_EXPERT,
                "color": AgentType.COLOR_STYLIST,
                "palette": AgentType.COLOR_STYLIST,
                "layout": AgentType.LAYOUT_MANAGER,
                "arrange": AgentType.LAYOUT_MANAGER,
                "component": AgentType.COMPONENT_BUILDER,
                "button": AgentType.COMPONENT_BUILDER,
                "export": AgentType.EXPORT_SPECIALIST,
                "prototype": AgentType.PROTOTYPE_BUILDER,
                "icon": AgentType.ICON_DESIGNER,
                "image": AgentType.IMAGE_PROCESSOR,
                "animation": AgentType.ANIMATION_CREATOR,
                "grid": AgentType.GRID_DESIGNER,
                "spacing": AgentType.SPACING_MANAGER,
                "border": AgentType.BORDER_STYLIST,
                "shadow": AgentType.SHADOW_ARTIST,
                "gradient": AgentType.GRADIENT_CREATOR,
                "accessibility": AgentType.ACCESSIBILITY_CHECKER,
                "responsive": AgentType.RESPONSIVE_DESIGNER
            }

            request_lower = request.lower()
            required_agents = []

            for keyword, agent_type in agent_mapping.items():
                if keyword in request_lower:
                    required_agents.append(agent_type.value)

            # Default to shape creator if no specific agents identified
            if not required_agents:
                required_agents.append(AgentType.SHAPE_CREATOR.value)

            result = {
                "required_agents": required_agents,
                "analysis": f"Identified {len(required_agents)} relevant agents for the request",
                "request": request
            }

            return json.dumps(result)

        return analyze_design_request

    async def route_request(self, request: str) -> List[AgentTask]:
        """Route a design request to appropriate agents"""
        try:
            # Analyze the request
            analysis_result = self.routing_agent.run(f"Analyze this design request: {request}")

            # Parse the analysis (in a real implementation, this would be more sophisticated)
            tasks = []
            task_id = str(uuid.uuid4())

            # Create tasks based on request analysis
            # For demo purposes, create a few sample tasks
            if "shape" in request.lower() or "rectangle" in request.lower():
                tasks.append(AgentTask(
                    id=f"{task_id}_shape",
                    agent_type=AgentType.SHAPE_CREATOR,
                    description=f"Create shapes based on: {request}",
                    parameters={"request": request}
                ))

            if "text" in request.lower():
                tasks.append(AgentTask(
                    id=f"{task_id}_text",
                    agent_type=AgentType.TEXT_HANDLER,
                    description=f"Handle text elements for: {request}",
                    parameters={"request": request}
                ))

            if "color" in request.lower():
                tasks.append(AgentTask(
                    id=f"{task_id}_color",
                    agent_type=AgentType.COLOR_STYLIST,
                    description=f"Apply colors and styling for: {request}",
                    parameters={"request": request}
                ))

            # If no specific tasks identified, create a general design task
            if not tasks:
                tasks.append(AgentTask(
                    id=f"{task_id}_general",
                    agent_type=AgentType.SHAPE_CREATOR,
                    description=f"Create design elements for: {request}",
                    parameters={"request": request}
                ))

            logger.info(f"Routed request to {len(tasks)} agents")
            return tasks

        except Exception as e:
            logger.error(f"Error routing request: {str(e)}")
            raise

class FigmaMultiAgentSystem:
    """Main system orchestrating all agents and operations"""

    def __init__(self):
        self.agent_registry = AgentRegistry()
        self.router = IntelligentRouter(self.agent_registry)
        self.active_requests: Dict[str, FigmaDesignRequest] = {}
        self.task_executor = None

        # Initialize MCP client
        self.mcp_client = MCPClient(MCP_SERVER_COMMAND, MCP_SERVER_PATH)
        self.mcp_connected = False

    async def start_system(self):
        """Start the multi-agent system"""
        logger.info("Starting Figma Multi-Agent System...")

        # Try to connect to MCP server
        try:
            self.mcp_connected = await self.mcp_client.connect()
            if self.mcp_connected:
                logger.info("MCP server connected successfully")
                logger.info(f"Available MCP tools: {[tool.get('name', 'Unknown') for tool in self.mcp_client.available_tools]}")
            else:
                logger.warning("MCP server connection failed - continuing without MCP integration")
        except Exception as e:
            logger.warning(f"MCP server connection failed: {e} - continuing without MCP integration")
            self.mcp_connected = False

        self.task_executor = asyncio.create_task(self._task_processor())
        logger.info("System started successfully")

    async def stop_system(self):
        """Stop the multi-agent system"""
        if self.task_executor:
            self.task_executor.cancel()

        # Disconnect from MCP server
        if self.mcp_connected:
            self.mcp_client.disconnect()
            self.mcp_connected = False

        logger.info("System stopped")

    async def _task_processor(self):
        """Background task processor"""
        while True:
            try:
                # Process tasks from queue
                if not self.agent_registry.task_queue.empty():
                    task = await self.agent_registry.task_queue.get()
                    await self.agent_registry.execute_task(task)

                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in task processor: {str(e)}")

    async def process_design_request(self, description: str, requirements: List[str] = None) -> str:
        """Process a complete design request"""
        try:
            request_id = str(uuid.uuid4())

            # Route the request to appropriate agents
            tasks = await self.router.route_request(description)

            # Create design request
            design_request = FigmaDesignRequest(
                id=request_id,
                description=description,
                requirements=requirements or [],
                tasks=tasks
            )

            self.active_requests[request_id] = design_request

            # Execute tasks concurrently
            task_results = await asyncio.gather(
                *[self.agent_registry.execute_task(task) for task in tasks],
                return_exceptions=True
            )

            # Update request status
            design_request.status = "completed"

            # Compile results
            results = {
                "request_id": request_id,
                "description": description,
                "tasks_completed": len([r for r in task_results if not isinstance(r, Exception)]),
                "tasks_failed": len([r for r in task_results if isinstance(r, Exception)]),
                "results": [str(r) for r in task_results if not isinstance(r, Exception)],
                "errors": [str(r) for r in task_results if isinstance(r, Exception)]
            }

            logger.info(f"Design request {request_id} completed")
            return json.dumps(results, indent=2)

        except Exception as e:
            logger.error(f"Error processing design request: {str(e)}")
            raise

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "agents_count": len(self.agent_registry.agents),
            "active_requests": len(self.active_requests),
            "queue_size": self.agent_registry.task_queue.qsize(),
            "available_agents": [agent_type.value for agent_type in self.agent_registry.agents.keys()],
            "mcp_connected": self.mcp_connected,
            "mcp_tools_available": len(self.mcp_client.available_tools) if self.mcp_connected else 0,
            "timestamp": datetime.now().isoformat()
        }

    async def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call an MCP tool if connected"""
        if not self.mcp_connected:
            raise Exception("MCP server not connected")

        try:
            result = await self.mcp_client.call_tool(tool_name, arguments)
            logger.info(f"MCP tool {tool_name} called successfully")
            return result
        except Exception as e:
            logger.error(f"Failed to call MCP tool {tool_name}: {e}")
            raise

    def get_available_mcp_tools(self) -> List[Dict[str, Any]]:
        """Get list of available MCP tools"""
        if not self.mcp_connected:
            return []
        return self.mcp_client.available_tools

# Initialize the global system
figma_system = FigmaMultiAgentSystem()

# Set MCP system reference in tools module
set_mcp_system(figma_system)

# FastAPI REST API
app = FastAPI(
    title="Figma Multi-Agent Design System",
    description="REST API for the Figma Multi-Agent Design System",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    await figma_system.start_system()

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    await figma_system.stop_system()

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Figma Multi-Agent Design System API", "status": "running"}

@app.get("/status")
async def get_status():
    """Get system status"""
    return figma_system.get_system_status()

@app.post("/design")
async def create_design(request: dict):
    """Create a new design using the multi-agent system"""
    try:
        description = request.get("description", "")
        requirements = request.get("requirements", [])

        if not description:
            raise HTTPException(status_code=400, detail="Description is required")

        result = await figma_system.process_design_request(description, requirements)
        return {"success": True, "result": json.loads(result)}

    except Exception as e:
        logger.error(f"API error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agents")
async def list_agents():
    """List all available agents"""
    return {
        "agents": [
            {
                "type": agent_type.value,
                "name": agent_type.value.replace("_", " ").title(),
                "description": f"Specialized agent for {agent_type.value.replace('_', ' ')}"
            }
            for agent_type in AgentType
        ]
    }

@app.get("/mcp/tools")
async def list_mcp_tools():
    """List all available MCP tools"""
    try:
        tools = figma_system.get_available_mcp_tools()
        return {
            "success": True,
            "mcp_connected": figma_system.mcp_connected,
            "tools": tools
        }
    except Exception as e:
        logger.error(f"Error listing MCP tools: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/mcp/call")
async def call_mcp_tool(request: dict):
    """Call an MCP tool"""
    try:
        tool_name = request.get("tool_name", "")
        arguments = request.get("arguments", {})

        if not tool_name:
            raise HTTPException(status_code=400, detail="tool_name is required")

        result = await figma_system.call_mcp_tool(tool_name, arguments)
        return {
            "success": True,
            "tool_name": tool_name,
            "result": result
        }

    except Exception as e:
        logger.error(f"Error calling MCP tool: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/mcp/connect")
async def connect_mcp():
    """Manually connect to MCP server"""
    try:
        if figma_system.mcp_connected:
            return {
                "success": True,
                "message": "MCP server already connected",
                "status": "connected",
                "tools_available": len(figma_system.get_available_mcp_tools())
            }

        await figma_system.start_system()

        return {
            "success": True,
            "message": "MCP server connected successfully",
            "status": "connected",
            "tools_available": len(figma_system.get_available_mcp_tools()),
            "available_tools": [tool.get('name', 'Unknown') for tool in figma_system.get_available_mcp_tools()[:10]]
        }

    except Exception as e:
        logger.error(f"Error connecting to MCP server: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to connect to MCP server: {str(e)}")

@app.post("/mcp/disconnect")
async def disconnect_mcp():
    """Manually disconnect from MCP server"""
    try:
        if not figma_system.mcp_connected:
            return {
                "success": True,
                "message": "MCP server already disconnected",
                "status": "disconnected"
            }

        await figma_system.stop_system()

        return {
            "success": True,
            "message": "MCP server disconnected successfully",
            "status": "disconnected"
        }

    except Exception as e:
        logger.error(f"Error disconnecting from MCP server: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to disconnect from MCP server: {str(e)}")

@app.get("/mcp/status")
async def get_mcp_status():
    """Get current MCP connection status"""
    try:
        mcp_tools = figma_system.get_available_mcp_tools() if figma_system.mcp_connected else []

        return {
            "success": True,
            "status": "connected" if figma_system.mcp_connected else "disconnected",
            "tools_available": len(mcp_tools),
            "server_command": MCP_SERVER_COMMAND,
            "server_path": MCP_SERVER_PATH,
            "sample_tools": [tool.get('name', 'Unknown') for tool in mcp_tools[:5]] if mcp_tools else []
        }

    except Exception as e:
        logger.error(f"Error getting MCP status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def run_api_server():
    """Run the FastAPI server"""
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

def create_streamlit_interface():
    """Create the Streamlit user interface"""
    st.set_page_config(
        page_title="Figma Multi-Agent App",
        page_icon="🎨",
        layout="wide"
    )

    st.title("🎨 Figma Multi-Agent App")

    # Sidebar with system information
    with st.sidebar:
        st.header("System Status")

        if st.button("Refresh Status"):
            status = figma_system.get_system_status()
            st.json(status)

        # MCP Status
        st.header("MCP Integration")
        mcp_status = "🟢 Connected" if figma_system.mcp_connected else "🔴 Disconnected"
        st.write(f"**Status:** {mcp_status}")

        # MCP Connection Controls
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Connect MCP", disabled=figma_system.mcp_connected):
                with st.spinner("Connecting to MCP server..."):
                    try:
                        async_to_sync(figma_system.start_system)()
                        st.success("MCP Connected!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Connection failed: {str(e)}")

        with col2:
            if st.button("Disconnect MCP", disabled=not figma_system.mcp_connected):
                with st.spinner("Disconnecting from MCP server..."):
                    try:
                        async_to_sync(figma_system.stop_system)()
                        st.success("MCP Disconnected!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Disconnection failed: {str(e)}")

        if figma_system.mcp_connected:
            mcp_tools = figma_system.get_available_mcp_tools()
            st.write(f"**Available MCP Tools:** {len(mcp_tools)}")
            if mcp_tools:
                with st.expander("View MCP Tools"):
                    for tool in mcp_tools[:5]:  # Show first 5 tools
                        tool_name = tool.get('name', 'Unknown')
                        tool_desc = tool.get('description', 'No description')
                        st.write(f"**{tool_name}:** {tool_desc}")
                    if len(mcp_tools) > 5:
                        st.write(f"... and {len(mcp_tools) - 5} more tools")

        st.header("Available Agents")
        agent_types = [agent_type.value.replace("_", " ").title() for agent_type in AgentType]
        st.write(f"**{len(agent_types)} Specialized Agents:**")
        for agent in agent_types[:10]:  # Show first 10
            st.write(f"• {agent}")
        if len(agent_types) > 10:
            st.write(f"• ... and {len(agent_types) - 10} more")

    # Main interface
    # st.header("Create Your Design")

    # Design request input
    design_description = st.text_area(
        "Describe your design request:",
        placeholder="e.g., Create a modern login form with blue color scheme, rounded buttons, and clean typography",
        height=100
    )

    # Requirements input
    requirements = st.text_input(
        "Additional requirements (comma-separated):",
        placeholder="responsive, accessible, modern, minimalist"
    )

    # Process button
    if st.button("🚀 Generate Design", type="primary"):
        if design_description:
            with st.spinner("Processing your design request..."):
                try:
                    # Initialize system if not already done
                    if not hasattr(st.session_state, 'system_initialized'):
                        asyncio.run(figma_system.start_system())
                        st.session_state.system_initialized = True

                    # Process the request
                    requirements_list = [req.strip() for req in requirements.split(",") if req.strip()] if requirements else []
                    result = asyncio.run(figma_system.process_design_request(design_description, requirements_list))

                    # Display results
                    st.success("Design request processed successfully!")

                    # Parse and display results
                    result_data = json.loads(result)

                    col1, col2 = st.columns(2)

                    with col1:
                        st.metric("Tasks Completed", result_data.get("tasks_completed", 0))
                        st.metric("Tasks Failed", result_data.get("tasks_failed", 0))

                    with col2:
                        st.metric("Request ID", result_data.get("request_id", "N/A"))

                    # Show detailed results
                    if result_data.get("results"):
                        st.subheader("Agent Results")
                        for i, agent_result in enumerate(result_data["results"]):
                            with st.expander(f"Agent Result {i+1}"):
                                try:
                                    parsed_result = json.loads(agent_result)
                                    st.json(parsed_result)
                                except:
                                    st.text(agent_result)

                    # Show errors if any
                    if result_data.get("errors"):
                        st.subheader("Errors")
                        for error in result_data["errors"]:
                            st.error(error)

                except Exception as e:
                    st.error(f"Error processing request: {str(e)}")
                    logger.error(f"Streamlit error: {str(e)}")
        else:
            st.warning("Please enter a design description")



def main():
    """Main application entry point"""
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "api":
            # Run API server
            print("Starting FastAPI server...")
            run_api_server()
        elif sys.argv[1] == "streamlit":
            # Run Streamlit interface
            print("Starting Streamlit interface...")
            create_streamlit_interface()
        else:
            print("Usage: python app.py [api|streamlit]")
    else:
        # Default: run Streamlit interface
        print("Starting Streamlit interface (default)...")
        create_streamlit_interface()

if __name__ == "__main__":
    main()