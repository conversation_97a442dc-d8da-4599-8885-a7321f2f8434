#!/usr/bin/env python3
"""
Simplified Figma Multi-Agent Design System
Single router endpoint with OpenAI SDK MCP integration
Clean request-response pattern with intelligent task delegation
"""

import json
import logging
import os
from datetime import datetime

# Core imports
import google.generativeai as genai
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import our modular components
from agents import Agent, AgentType, AGENT_SPECIFICATIONS
from tools import get_tools_for_agent, initialize_mcp_tools

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('figma_agents.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure Gemini API
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# MCP Configuration - Hardcoded server URL (to be provided by user)
MCP_SERVER_URL = "C:/Users/<USER>/Desktop/claudetalktofigma/claude-talk-to-figma-mcp/src/talk_to_figma_mcp/server.ts"  # User will provide this URL

class SimpleRouter:
    """Simple intelligent router for task delegation"""

    def __init__(self):
        self.agents = {}
        self._initialize_agents()

    def _initialize_agents(self):
        """Initialize all specialized agents"""
        logger.info("Initializing agents...")

        for agent_type, spec in AGENT_SPECIFICATIONS.items():
            try:
                # Get tools for this agent type
                tools = get_tools_for_agent(agent_type.value)

                # Create agent instance
                agent = Agent(
                    name=spec["name"],
                    instructions=spec["instructions"],
                    tools=tools
                )

                self.agents[agent_type.value] = agent
                logger.info(f"Initialized agent: {spec['name']}")

            except Exception as e:
                logger.error(f"Failed to initialize agent {spec['name']}: {e}")

        logger.info(f"Successfully initialized {len(self.agents)} agents")

    def route_message(self, user_message: str) -> str:
        """Route user message to appropriate agent and return response"""
        try:
            # Simple keyword-based routing logic
            message_lower = user_message.lower()

            # Determine best agent based on keywords
            if any(word in message_lower for word in ['shape', 'rectangle', 'circle', 'polygon']):
                agent_type = 'shape_creator'
            elif any(word in message_lower for word in ['text', 'font', 'typography']):
                agent_type = 'text_handler'
            elif any(word in message_lower for word in ['color', 'fill', 'background']):
                agent_type = 'color_stylist'
            elif any(word in message_lower for word in ['layout', 'arrange', 'position']):
                agent_type = 'layout_manager'
            elif any(word in message_lower for word in ['component', 'instance']):
                agent_type = 'component_builder'
            elif any(word in message_lower for word in ['layer', 'organize', 'group']):
                agent_type = 'layer_organizer'
            elif any(word in message_lower for word in ['export', 'download', 'save']):
                agent_type = 'export_specialist'
            elif any(word in message_lower for word in ['prototype', 'interaction', 'link']):
                agent_type = 'prototype_builder'
            elif any(word in message_lower for word in ['icon', 'symbol']):
                agent_type = 'icon_designer'
            elif any(word in message_lower for word in ['image', 'photo', 'picture']):
                agent_type = 'image_processor'
            else:
                # Default to layout manager for general design requests
                agent_type = 'layout_manager'

            # Get the appropriate agent
            agent = self.agents.get(agent_type)
            if not agent:
                return json.dumps({
                    "error": f"Agent {agent_type} not available",
                    "message": user_message
                })

            # Execute task with the agent
            logger.info(f"Routing message to {agent.name}: {user_message}")
            result = agent.run(user_message)

            return json.dumps({
                "success": True,
                "agent_used": agent.name,
                "agent_type": agent_type,
                "user_message": user_message,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Error routing message: {e}")
            return json.dumps({
                "error": str(e),
                "message": user_message,
                "timestamp": datetime.now().isoformat()
            })

# Initialize the router
router = SimpleRouter()

# Initialize MCP tools with hardcoded URL
initialize_mcp_tools(MCP_SERVER_URL)

# FastAPI Application
app = FastAPI(
    title="Simplified Figma Multi-Agent Design System",
    description="Single router endpoint with intelligent task delegation",
    version="2.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/chat")
async def chat_endpoint(request: dict):
    """
    Main router endpoint - handles all user interactions
    Routes messages to appropriate specialized agents
    """
    try:
        message = request.get("message", "")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Route message to appropriate agent
        response = router.route_message(message)

        return {"response": json.loads(response)}

    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status")
async def get_status():
    """Get system status"""
    return {
        "status": "active",
        "agents_available": len(router.agents),
        "agent_types": list(router.agents.keys()),
        "mcp_server_url": MCP_SERVER_URL,
        "timestamp": datetime.now().isoformat()
    }

def main():
    """Main application entry point"""
    print("Starting Simplified Figma Multi-Agent Design System...")
    print("Available at: http://localhost:8000")
    print("Main endpoint: POST /chat")
    print("Status endpoint: GET /status")

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

if __name__ == "__main__":
    main()