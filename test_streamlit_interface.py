#!/usr/bin/env python3
"""
Test script to verify the Streamlit interface modifications
Tests that the interface loads correctly and core functionality is preserved
"""

import sys
import os
import importlib.util
import asyncio
import json

def test_streamlit_interface():
    """Test the Streamlit interface functionality"""
    print("🧪 Testing Streamlit Interface Modifications")
    print("=" * 60)
    
    try:
        # Import the main module
        from figma_multi_agent_app import create_streamlit_interface, FigmaMultiAgentSystem
        print("✅ Successfully imported Streamlit interface function")
        
        # Test that the function exists and is callable
        if callable(create_streamlit_interface):
            print("✅ create_streamlit_interface is callable")
        else:
            print("❌ create_streamlit_interface is not callable")
            return False
            
        # Test system initialization
        system = FigmaMultiAgentSystem()
        print("✅ FigmaMultiAgentSystem can be instantiated")
        
        # Test system status (synchronous method)
        status = system.get_system_status()
        print(f"✅ System status retrieved: {status['agents_count']} agents available")
        
        print("\n🔍 Verifying Interface Modifications...")
        
        # Read the source code to verify modifications
        with open('figma_multi_agent_app.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Check that branding text was removed
        if "Powered by 25+ Specialized AI Agents" not in source_code:
            print("✅ Branding text 'Powered by 25+ Specialized AI Agents' removed")
        else:
            print("❌ Branding text still present")
            return False
        
        # Check that example requests section was removed
        if "Example Requests" not in source_code:
            print("✅ 'Example Requests' section removed")
        else:
            print("❌ 'Example Requests' section still present")
            return False
        
        # Check that example buttons were removed
        if "Try: " not in source_code or "st.button(f\"Try: {example}\"" not in source_code:
            print("✅ Example request buttons removed")
        else:
            print("❌ Example request buttons still present")
            return False
        
        # Check that core functionality is preserved
        if "st.text_area(" in source_code and "Describe your design request:" in source_code:
            print("✅ Main design request input field preserved")
        else:
            print("❌ Main design request input field missing")
            return False
        
        if "Generate Design" in source_code and "st.button(" in source_code:
            print("✅ Generate Design button preserved")
        else:
            print("❌ Generate Design button missing")
            return False
        
        # Check that async operations are properly handled
        if "asyncio.run(figma_system.start_system())" in source_code:
            print("✅ Async system initialization properly handled with asyncio.run()")
        else:
            print("❌ Async system initialization not properly handled")
            return False
        
        if "asyncio.run(figma_system.process_design_request(" in source_code:
            print("✅ Async design request processing properly handled with asyncio.run()")
        else:
            print("❌ Async design request processing not properly handled")
            return False
        
        # Check that no raw await keywords are used in Streamlit context
        lines = source_code.split('\n')
        streamlit_function_started = False
        raw_await_found = False
        
        for i, line in enumerate(lines):
            if "def create_streamlit_interface():" in line:
                streamlit_function_started = True
            elif streamlit_function_started and line.strip().startswith("def ") and "create_streamlit_interface" not in line:
                break  # End of streamlit function
            elif streamlit_function_started and " await " in line and "asyncio.run(" not in line:
                print(f"❌ Raw await found in Streamlit function at line {i+1}: {line.strip()}")
                raw_await_found = True
        
        if not raw_await_found:
            print("✅ No improper await usage found in Streamlit interface")
        else:
            return False
        
        print("\n🎯 Interface Simplification Verification:")
        
        # Count lines to verify simplification
        total_lines = len(lines)
        streamlit_lines = 0
        in_streamlit_function = False
        
        for line in lines:
            if "def create_streamlit_interface():" in line:
                in_streamlit_function = True
            elif in_streamlit_function and line.strip().startswith("def ") and "create_streamlit_interface" not in line:
                break
            elif in_streamlit_function:
                streamlit_lines += 1
        
        print(f"✅ Streamlit interface function: {streamlit_lines} lines (simplified)")
        print(f"✅ Total application: {total_lines} lines")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_interface_components():
    """Test individual interface components"""
    print("\n🔧 Testing Interface Components...")
    
    try:
        import streamlit as st
        print("✅ Streamlit import successful")
        
        # Test that we can import the necessary components
        from figma_multi_agent_app import AgentType, FigmaMultiAgentSystem
        print("✅ Core components import successful")
        
        # Test AgentType enum
        agent_types = list(AgentType)
        print(f"✅ {len(agent_types)} agent types available")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Streamlit Interface Modification Test")
    print("Testing simplified and cleaned interface...")
    print()
    
    # Test interface modifications
    interface_test = test_streamlit_interface()
    
    # Test components
    component_test = test_interface_components()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Interface Modifications: {'✅ PASSED' if interface_test else '❌ FAILED'}")
    print(f"  Component Functionality: {'✅ PASSED' if component_test else '❌ FAILED'}")
    
    if interface_test and component_test:
        print("\n🎉 All tests passed! The Streamlit interface has been successfully simplified.")
        print("\n🚀 The interface now features:")
        print("  • Clean, minimal design without branding text")
        print("  • No example requests section (reduced clutter)")
        print("  • Proper async/await handling with asyncio.run()")
        print("  • Preserved core functionality (input field + generate button)")
        print("  • Maintained system status sidebar")
        
        print("\n💡 To run the simplified interface:")
        print("  uv run streamlit run figma_multi_agent_app.py")
        
        return 0
    else:
        print("\n❌ Some tests failed. Please review the modifications.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
