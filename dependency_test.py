#!/usr/bin/env python3
"""
Comprehensive dependency test for Figma Multi-Agent Design System
Tests all required dependencies and their key functionality
"""

import sys
import importlib
from typing import Dict, List, Tuple

def test_import(module_name: str, description: str = "") -> Tuple[bool, str]:
    """Test importing a module and return success status with message"""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'unknown')
        return True, f"✅ {module_name} (v{version}) - {description}"
    except ImportError as e:
        return False, f"❌ {module_name} - FAILED: {str(e)}"
    except Exception as e:
        return False, f"⚠️  {module_name} - WARNING: {str(e)}"

def test_functionality():
    """Test key functionality of imported modules"""
    print("\n🔧 Testing Key Functionality...")
    
    # Test Google Generative AI
    try:
        import google.generativeai as genai
        print("  ✅ Google Generative AI - Configuration methods available")
    except Exception as e:
        print(f"  ❌ Google Generative AI functionality test failed: {e}")
    
    # Test FastAPI
    try:
        from fastapi import FastAPI
        app = FastAPI()
        print("  ✅ FastAPI - App creation successful")
    except Exception as e:
        print(f"  ❌ FastAPI functionality test failed: {e}")
    
    # Test Streamlit (basic import only, as it requires special handling)
    try:
        import streamlit as st
        print("  ✅ Streamlit - Import successful")
    except Exception as e:
        print(f"  ❌ Streamlit functionality test failed: {e}")
    
    # Test Uvicorn
    try:
        import uvicorn
        print("  ✅ Uvicorn - Server module available")
    except Exception as e:
        print(f"  ❌ Uvicorn functionality test failed: {e}")
    
    # Test python-dotenv
    try:
        from dotenv import load_dotenv
        print("  ✅ Python-dotenv - Environment loading available")
    except Exception as e:
        print(f"  ❌ Python-dotenv functionality test failed: {e}")

def main():
    """Main test function"""
    print("🧪 Figma Multi-Agent System - Dependency Test")
    print("=" * 60)
    
    # Core Python modules
    core_modules = [
        ("asyncio", "Asynchronous I/O"),
        ("json", "JSON handling"),
        ("logging", "Logging system"),
        ("os", "Operating system interface"),
        ("uuid", "UUID generation"),
        ("datetime", "Date and time handling"),
        ("typing", "Type hints"),
        ("dataclasses", "Data classes"),
        ("enum", "Enumerations"),
        ("traceback", "Exception tracing"),
    ]
    
    # External dependencies
    external_modules = [
        ("google.generativeai", "Google Gemini AI integration"),
        ("streamlit", "Web UI framework"),
        ("fastapi", "REST API framework"),
        ("uvicorn", "ASGI server"),
        ("dotenv", "Environment variable loading"),
        ("blinker", "Signal/event system (Streamlit dependency)"),
    ]
    
    # Additional dependencies that might be needed
    additional_modules = [
        ("pandas", "Data manipulation (Streamlit dependency)"),
        ("numpy", "Numerical computing (Streamlit dependency)"),
        ("requests", "HTTP client library"),
        ("pydantic", "Data validation (FastAPI dependency)"),
        ("jinja2", "Template engine"),
        ("click", "Command line interface"),
    ]
    
    print("📦 Testing Core Python Modules...")
    core_passed = 0
    for module, desc in core_modules:
        success, message = test_import(module, desc)
        print(f"  {message}")
        if success:
            core_passed += 1
    
    print(f"\n📦 Testing External Dependencies...")
    external_passed = 0
    for module, desc in external_modules:
        success, message = test_import(module, desc)
        print(f"  {message}")
        if success:
            external_passed += 1
    
    print(f"\n📦 Testing Additional Dependencies...")
    additional_passed = 0
    for module, desc in additional_modules:
        success, message = test_import(module, desc)
        print(f"  {message}")
        if success:
            additional_passed += 1
    
    # Test functionality
    test_functionality()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Dependency Test Summary:")
    print(f"  Core Python modules: {core_passed}/{len(core_modules)} ✅")
    print(f"  External dependencies: {external_passed}/{len(external_modules)} ✅")
    print(f"  Additional dependencies: {additional_passed}/{len(additional_modules)} ✅")
    
    total_critical = len(core_modules) + len(external_modules)
    total_critical_passed = core_passed + external_passed
    
    if total_critical_passed == total_critical:
        print("\n🎉 All critical dependencies are working correctly!")
        print("✅ The Figma Multi-Agent System is ready to run.")
        
        print("\n🚀 Usage Commands:")
        print("  Test system:     uv run python simple_test.py")
        print("  Streamlit UI:    uv run streamlit run figma_multi_agent_app.py")
        print("  FastAPI server:  uv run python figma_multi_agent_app.py api")
        
    else:
        print(f"\n❌ {total_critical - total_critical_passed} critical dependencies failed!")
        print("Please install missing dependencies before running the application.")
        
        if external_passed < len(external_modules):
            print("\n💡 To install missing external dependencies:")
            for module, desc in external_modules:
                success, _ = test_import(module, desc)
                if not success:
                    package_name = module.replace('.', '-') if '.' in module else module
                    print(f"  uv add {package_name}")
    
    print("\n" + "=" * 60)
    return 0 if total_critical_passed == total_critical else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
