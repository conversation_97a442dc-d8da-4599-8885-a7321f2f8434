#!/usr/bin/env python3
"""
Simple test for the core Figma Multi-Agent functionality
"""

import asyncio
import json
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test core components without UI dependencies
def test_agent_types():
    """Test that all agent types are properly defined"""
    print("🔍 Testing Agent Types...")
    
    try:
        from figma_multi_agent_app import AgentType
        
        agent_types = list(AgentType)
        print(f"✅ Total agent types defined: {len(agent_types)}")
        
        expected_count = 25  # We expect 25 specialized agents
        if len(agent_types) >= expected_count:
            print(f"✅ Agent count meets requirement: {len(agent_types)} >= {expected_count}")
        else:
            print(f"❌ Agent count below requirement: {len(agent_types)} < {expected_count}")
        
        # Show first 10 agents
        print("📋 Sample agents:")
        for i, agent in enumerate(agent_types[:10]):
            print(f"  {i+1}. {agent.value.replace('_', ' ').title()}")
        
        if len(agent_types) > 10:
            print(f"  ... and {len(agent_types) - 10} more agents")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agent types: {str(e)}")
        return False

async def test_system_initialization():
    """Test system initialization without UI"""
    print("\n🚀 Testing System Initialization...")
    
    try:
        from figma_multi_agent_app import FigmaMultiAgentSystem
        
        # Create system instance
        system = FigmaMultiAgentSystem()
        print("✅ System instance created")
        
        # Test system status before starting
        status = system.get_system_status()
        print(f"✅ System status retrieved: {status['agents_count']} agents")
        
        # Start system
        await system.start_system()
        print("✅ System started successfully")
        
        # Test system status after starting
        status = system.get_system_status()
        print(f"✅ Active system status: {status['agents_count']} agents, {status['active_requests']} requests")
        
        # Stop system
        await system.stop_system()
        print("✅ System stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing system initialization: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_request():
    """Test a basic design request"""
    print("\n🎨 Testing Basic Design Request...")
    
    try:
        from figma_multi_agent_app import FigmaMultiAgentSystem
        
        system = FigmaMultiAgentSystem()
        await system.start_system()
        
        # Test simple request
        request = "Create a blue rectangle"
        print(f"📝 Processing request: '{request}'")
        
        result = await system.process_design_request(request)
        result_data = json.loads(result)
        
        print(f"✅ Request processed successfully")
        print(f"   Request ID: {result_data.get('request_id', 'N/A')}")
        print(f"   Tasks completed: {result_data.get('tasks_completed', 0)}")
        print(f"   Tasks failed: {result_data.get('tasks_failed', 0)}")
        
        if result_data.get('results'):
            print(f"   Results generated: {len(result_data['results'])}")
        
        await system.stop_system()
        return True
        
    except Exception as e:
        print(f"❌ Error testing basic request: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test that all required modules can be imported"""
    print("📦 Testing Imports...")
    
    required_modules = [
        'asyncio', 'json', 'logging', 'os', 'uuid', 'datetime',
        'typing', 'dataclasses', 'enum', 'traceback'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            return False
    
    # Test optional modules
    optional_modules = ['google.generativeai', 'fastapi', 'uvicorn', 'python-dotenv']
    
    for module in optional_modules:
        try:
            __import__(module.replace('-', '_'))
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ⚠️  {module} (optional)")
    
    return True

async def main():
    """Main test function"""
    print("🧪 Figma Multi-Agent System - Core Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Agent Types
    if test_agent_types():
        tests_passed += 1
    
    # Test 3: System Initialization
    if await test_system_initialization():
        tests_passed += 1
    
    # Test 4: Basic Request (only if API key is available)
    if os.getenv('GEMINI_API_KEY'):
        if await test_basic_request():
            tests_passed += 1
    else:
        print("\n⚠️  Skipping basic request test - GEMINI_API_KEY not found")
        total_tests -= 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The system is ready to use.")
        print("\n🚀 To run the full application:")
        print("   Streamlit UI: streamlit run figma_multi_agent_app.py")
        print("   FastAPI: python figma_multi_agent_app.py api")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
