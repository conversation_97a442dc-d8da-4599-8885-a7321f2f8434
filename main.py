# def main():
#     print("Hello from openai-sdk!")
from agents import Agent, ModelSettings, function_tool

@function_tool
def get_weather(city: str) -> str:
    return f"The weather in {city} is sunny"

agent = Agent(
    name="Haiku agent",
    instructions="Always respond in haiku form",
    model="o3-mini",
    tools=[get_weather],
)

def main():
    print(agent.run("What's the weather in San Francisco?"))

if __name__ == "__main__":
    main()
